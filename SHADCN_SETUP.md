# shadcn/ui 配置文档

本项目已成功配置 shadcn/ui 组件库。以下是配置详情：

## 已安装的依赖

### 核心依赖
- `tailwindcss@^3.4.0` - Tailwind CSS 框架
- `postcss` - CSS 后处理器
- `autoprefixer` - CSS 自动前缀
- `tailwindcss-animate` - Tailwind CSS 动画插件

### shadcn/ui 相关依赖
- `class-variance-authority` - 类变体管理
- `clsx` - 条件类名工具
- `tailwind-merge` - Tailwind 类名合并
- `@radix-ui/react-slot` - Radix UI Slot 组件

## 配置文件

### 1. tailwind.config.js
配置了 shadcn/ui 的默认主题和颜色系统，包括：
- CSS 变量支持
- 动画配置
- 响应式容器
- 自定义颜色调色板

### 2. postcss.config.js
配置了 PostCSS 插件：
- tailwindcss
- autoprefixer

### 3. components.json
shadcn/ui 的配置文件，定义了：
- 组件路径：`src/webview/components/ui`
- 工具函数路径：`src/webview/lib/utils`
- CSS 文件路径：`src/webview/globals.css`

### 4. src/webview/globals.css
包含了 shadcn/ui 的全局样式和 CSS 变量定义。

## 已创建的组件

### 1. Button 组件 (`src/webview/components/ui/button.tsx`)
- 支持多种变体：default, destructive, outline, secondary, ghost, link
- 支持多种尺寸：sm, default, lg, icon
- 基于 class-variance-authority 构建

### 2. Card 组件 (`src/webview/components/ui/card.tsx`)
包含以下子组件：
- Card
- CardHeader
- CardTitle
- CardDescription
- CardContent
- CardFooter

### 3. 工具函数 (`src/webview/lib/utils.ts`)
- `cn()` 函数：用于合并和条件化 Tailwind CSS 类名

## 演示组件

### ShadcnDemo (`src/webview/components/ShadcnDemo.tsx`)
展示了 shadcn/ui 组件的使用示例，包括：
- 各种按钮变体和尺寸
- 卡片组件布局
- 主题颜色演示

## 构建配置

### package.json 脚本
- `build:css`: 使用 Tailwind CSS 构建样式文件
- `compile`: 完整构建流程（类型检查 + 代码检查 + CSS 构建 + JS 构建）

### esbuild 配置
- 支持 React JSX 自动转换
- 分离的 CSS 构建流程
- 开发和生产模式支持

## 使用方法

### 1. 导入组件
```tsx
import { Button } from './components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from './components/ui/card';
```

### 2. 使用工具函数
```tsx
import { cn } from './lib/utils';

// 合并类名
const className = cn('base-class', condition && 'conditional-class');
```

### 3. 添加新组件
可以手动创建新的 shadcn/ui 组件，或者参考官方文档：
https://ui.shadcn.com/docs/components

## 开发流程

1. 修改样式后运行 `pnpm run build:css` 重新构建 CSS
2. 运行 `pnpm run compile` 进行完整构建
3. 在 VS Code 中测试扩展功能

## 注意事项

- 使用 Tailwind CSS v3.4.0（而非 v4）以确保兼容性
- CSS 文件通过 webview 的 URI 系统加载
- 所有组件都支持 VS Code 的主题系统
- 确保在 HTML 模板中正确引用生成的 CSS 文件

## 故障排除

如果样式不生效：
1. 检查 `dist/webview.css` 是否存在
2. 确认 HTML 模板中的 `{{webviewCss}}` 占位符被正确替换
3. 检查 VS Code 的 Content Security Policy 设置
4. 运行 `pnpm run build:css` 重新构建样式
