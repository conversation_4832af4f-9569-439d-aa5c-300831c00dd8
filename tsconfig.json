{
	"compilerOptions": {
		"module": "ES2022",
		"target": "ES2022",
		"lib": [
			"ES2022",
			"DOM",
			"DOM.Iterable"
		],
		"sourceMap": true,
		"rootDir": "src",
		"jsx": "react-jsx",
		"allowSyntheticDefaultImports": true,
		"moduleResolution": "bundler",
		"esModuleInterop": true,
		"forceConsistentCasingInFileNames": true,
		"strict": true,
		"skipLibCheck": true,
		"outDir": "dist"
		/* Additional Checks */
		// "noImplicitReturns": true, /* Report error when not all code paths in function return a value. */
		// "noFallthroughCasesInSwitch": true, /* Report errors for fallthrough cases in switch statement. */
		// "noUnusedParameters": true,  /* Report errors on unused parameters. */
	},
	"include": [
		"src/**/*"
	],
	"exclude": [
		"node_modules",
		"dist",
		".mastra"
	]
}