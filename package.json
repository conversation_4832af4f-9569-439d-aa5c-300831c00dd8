{"name": "aicode", "displayName": "aicode", "description": "", "version": "0.0.1", "publisher": "aicode-dev", "icon": "icon.png", "engines": {"vscode": "^1.102.0"}, "categories": ["Other"], "activationEvents": ["*"], "main": "./dist/extension.js", "contributes": {"commands": [{"command": "aicode.test", "title": "测试命令"}, {"command": "aicode.openReactPage", "title": "打开React页面"}, {"command": "aicode.showWebview", "title": "显示AI Code面板"}, {"command": "aicode.configureApiKey", "title": "配置 OpenAI API Key"}], "viewsContainers": {"activitybar": [{"id": "aicode", "title": "AI Code", "icon": "icon.png"}]}, "views": {"aicode": [{"id": "aicode.chatView", "name": "React Panel", "when": "true", "type": "webview"}], "explorer": [{"id": "aicode.explorer<PERSON>iew", "name": "AI Code React", "when": "true"}]}, "menus": {"commandPalette": [{"command": "aicode.openReactPage", "when": "true"}]}, "configuration": {"title": "AI Code", "properties": {"aicode.openaiApiKey": {"type": "string", "default": "", "description": "OpenAI API Key for AI chat functionality", "scope": "application"}, "aicode.mastraPort": {"type": "number", "default": 4111, "description": "Mastra 服务器端口号", "scope": "application", "minimum": 1024, "maximum": 65535}, "aicode.useProductionMode": {"type": "boolean", "default": false, "description": "强制使用生产模式启动 Mastra 服务器", "scope": "application"}}}}, "scripts": {"dev": "<PERSON>ra dev", "build": "mastra build", "start": "mastra start", "packagex": "pnpm vsce package --no-dependencies", "vscode:prepublish": "pnpm run package", "compile": "pnpm run check-types && pnpm run lint &&pnpm run build:css && node esbuild.js", "build:css": "npx @tailwindcss/cli -i ./src/webview/globals.css -o ./dist/tailwindcss.css", "test": "vitest", "test:run": "vitest run", "test:watch": "vitest --watch", "watch": "npm-run-all -p watch:*", "watch:esbuild": "node esbuild.js --watch", "watch:tsc": "tsc --noEmit --watch --project tsconfig.json", "package": "pnpm run check-types && pnpm run lint  && node esbuild.js --production", "compile-tests": "tsc -p . --outDir out", "watch-tests": "tsc -p . -w --outDir out", "pretest": "pnpm run compile-tests && pnpm run compile && pnpm run lint", "check-types": "tsc --noEmit", "lint": "eslint src"}, "devDependencies": {"@shadcn/ui": "^0.0.4", "@tailwindcss/postcss": "^4.1.11", "@tailwindcss/typography": "^0.5.16", "@types/mocha": "^10.0.10", "@types/node": "20.x", "@types/react": "^19.1.9", "@types/react-dom": "^19.1.7", "@types/vscode": "^1.102.0", "@typescript-eslint/eslint-plugin": "^8.31.1", "@typescript-eslint/parser": "^8.31.1", "@vscode/test-cli": "^0.0.11", "@vscode/test-electron": "^2.5.2", "autoprefixer": "^10.4.21", "esbuild": "^0.25.3", "eslint": "^9.25.1", "npm-run-all": "^4.1.5", "postcss": "^8.5.6", "postcss-cli": "^11.0.1", "tailwindcss": "^4.1.11", "tailwindcss-animate": "^1.0.7", "typescript": "^5.8.3"}, "dependencies": {"@ai-sdk/openai": "^1.3.23", "@mastra/core": "^0.12.0", "@mastra/libsql": "^0.12.0", "@radix-ui/react-slot": "^1.2.3", "@tailwindcss/cli": "^4.1.11", "ai": "^4.3.19", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "mastra": "^0.10.16", "react": "^19.1.1", "react-dom": "^19.1.1", "react-markdown": "^10.1.0", "tailwind-merge": "^3.3.1", "vitest": "^3.2.4", "zod": "3.23.8"}}