import { vi } from 'vitest';

// Mock AI SDK modules
vi.mock('ai', () => ({
  generateText: vi.fn(),
  streamText: vi.fn(),
}));

vi.mock('@ai-sdk/openai', () => ({
  openai: vi.fn(() => ({
    modelId: 'deepseek-ai/DeepSeek-V3',
    provider: 'deepseek',
  })),
}));

// Global test setup
global.console = {
  ...console,
  // Suppress console.log in tests unless needed
  log: vi.fn(),
  warn: vi.fn(),
  error: vi.fn(),
};
