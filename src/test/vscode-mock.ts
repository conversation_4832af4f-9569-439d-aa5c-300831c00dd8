// Mock VS Code API for testing
export const workspace = {
  getConfiguration: () => ({
    get: () => undefined,
    update: () => Promise.resolve(),
    has: () => false,
    inspect: () => undefined,
  }),
  onDidChangeConfiguration: () => ({ dispose: () => {} }),
  workspaceFolders: [],
  name: 'test-workspace',
  rootPath: '/test',
};

export const window = {
  showInformationMessage: () => Promise.resolve(),
  showWarningMessage: () => Promise.resolve(),
  showErrorMessage: () => Promise.resolve(),
  showInputBox: () => Promise.resolve(),
  showQuickPick: () => Promise.resolve(),
  createWebviewPanel: () => ({
    webview: {
      html: '',
      postMessage: () => Promise.resolve(),
      onDidReceiveMessage: () => ({ dispose: () => {} }),
    },
    onDidDispose: () => ({ dispose: () => {} }),
    dispose: () => {},
  }),
};

export const commands = {
  registerCommand: () => ({ dispose: () => {} }),
  executeCommand: () => Promise.resolve(),
};

export const Uri = {
  file: (path: string) => ({ fsPath: path, scheme: 'file' }),
  parse: (uri: string) => ({ fsPath: uri, scheme: 'file' }),
};

export const ViewColumn = {
  One: 1,
  Two: 2,
  Three: 3,
};

export const WebviewPanelSerializer = {};

export const ConfigurationTarget = {
  Global: 1,
  Workspace: 2,
  WorkspaceFolder: 3,
};

export const Disposable = {
  from: () => ({ dispose: () => {} }),
};

export const EventEmitter = class {
  fire() {}
  event = () => ({ dispose: () => {} });
};

export const CancellationTokenSource = class {
  token = { isCancellationRequested: false };
  cancel() {}
  dispose() {}
};
