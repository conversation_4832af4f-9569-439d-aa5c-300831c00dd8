import { describe, it, expect } from 'vitest';
import * as vscode from 'vscode';

describe('Extension Test Suite', () => {
  it('should activate extension', async () => {
    // 这是一个基本的测试示例
    // 在实际的VS Code环境中，您可以测试扩展的功能
    expect(true).toBe(true);
  });

  it('should register commands', async () => {
    // 测试命令是否正确注册
    const commands = await vscode.commands.getCommands();
    expect(commands).toContain('aicode.helloWorld');
    expect(commands).toContain('aicode.openReactPage');
  });
});
