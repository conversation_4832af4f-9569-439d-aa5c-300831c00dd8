import * as vscode from 'vscode';
import { MastraServerManager } from '../services/mastraServerManager';
import { mastraClient } from '../services/mastraClient';

/**
 * 测试 Mastra 集成功能
 */
export async function testMastraIntegration(context: vscode.ExtensionContext): Promise<void> {
    try {
        // 1. 测试服务器管理器
        const serverManager = new MastraServerManager(context);

        await serverManager.start();

        // 等待服务器完全启动
        await new Promise(resolve => setTimeout(resolve, 3000));

        // 2. 测试服务器状态
        const isRunning = serverManager.isRunning();
        
        if (!isRunning) {
            throw new Error('Mastra 服务器未正常启动');
        }
        
        // 3. 测试客户端连接
        const isHealthy = await mastraClient.healthCheck();
        
        if (!isHealthy) {
            throw new Error('Mastra 服务器健康检查失败');
        }
        
        // 4. 测试获取 agents
        const agents = await mastraClient.getAgents();
        
        // 5. 测试 weather agent（如果存在）
        const weatherAgent = agents.find(a => a.id === 'weatherAgent');
        if (weatherAgent) {
            const response = await mastraClient.generateWithAgent('weatherAgent', {
                messages: [{ role: 'user', content: '你好，请介绍一下你自己' }]
            });
        }

        // 6. 停止服务器
        serverManager.stop();

        vscode.window.showInformationMessage('Mastra 集成测试通过！');

    } catch (error) {
        vscode.window.showErrorMessage(`Mastra 集成测试失败: ${error}`);
        throw error;
    }
}

/**
 * 注册测试命令
 */
export function registerMastraTestCommand(context: vscode.ExtensionContext): vscode.Disposable {
    return vscode.commands.registerCommand('aicode.testMastraIntegration', async () => {
        try {
            await testMastraIntegration(context);
        } catch (error) {
            // Test command execution failed
        }
    });
}
