import React from 'react';
import { Button } from './ui/button';
import { Card, CardContent } from './ui/card';

interface QuickActionsProps {
  onActionClick: (prompt: string) => void;
  disabled?: boolean;
}

const QuickActions: React.FC<QuickActionsProps> = ({ onActionClick, disabled }) => {
  const quickActions = [
    {
      icon: '🔍',
      title: '代码审查',
      prompt: '请帮我审查这段代码，指出可能的问题和改进建议'
    },
    {
      icon: '🚀',
      title: '性能优化',
      prompt: '请分析这段代码的性能，并提供优化建议'
    },
    {
      icon: '🐛',
      title: '调试帮助',
      prompt: '我的代码有bug，请帮我分析可能的原因'
    },
    {
      icon: '📚',
      title: '代码解释',
      prompt: '请详细解释这段代码的工作原理'
    },
    {
      icon: '✨',
      title: '重构建议',
      prompt: '请帮我重构这段代码，使其更加清晰和可维护'
    },
    {
      icon: '🧪',
      title: '编写测试',
      prompt: '请为这段代码编写单元测试'
    }
  ];

  return (
    <div className="mb-6">
      <div className="text-center mb-4">
        <h3 className="text-sm font-medium text-muted-foreground/80">✨ 快速开始</h3>
        <p className="text-xs text-muted-foreground/60 mt-1">选择一个操作快速开始对话</p>
      </div>
      <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
        {quickActions.map((action, index) => (
          <Button
            key={index}
            variant="outline"
            size="sm"
            className="h-auto p-4 flex flex-col items-center gap-2 text-xs bg-card/30 backdrop-blur-sm border-border/50 hover:bg-card/60 hover:border-purple-500/30 hover:shadow-lg hover:scale-105 transition-all duration-300 rounded-2xl group"
            onClick={() => onActionClick(action.prompt)}
            disabled={disabled}
          >
            <span className="text-2xl group-hover:scale-110 transition-transform duration-300">{action.icon}</span>
            <span className="text-center leading-tight font-medium text-foreground/80 group-hover:text-foreground">{action.title}</span>
          </Button>
        ))}
      </div>
    </div>
  );
};

export default QuickActions;
