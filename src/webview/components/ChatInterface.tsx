import React, { useState, useRef, useEffect } from 'react';
import { Button } from './ui/button';
import { Textarea } from './ui/textarea';
import { Badge } from './ui/badge';
import { Avatar, AvatarFallback } from './ui/avatar';
import { cn } from '../lib/utils';
import MessageContent from './MessageContent';

interface Message {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
}

interface ChatInterfaceProps {
  vscode: any;
}

const ChatInterface: React.FC<ChatInterfaceProps> = ({ vscode }) => {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: 'welcome',
      role: 'assistant',
      content: `👋 **欢迎使用 AI 代码助手！**

我可以帮助你：
- 🔍 **代码审查** - 分析代码质量和潜在问题
- 🚀 **性能优化** - 提供性能改进建议
- 🐛 **调试帮助** - 协助排查和修复bug
- 📚 **代码解释** - 详细解释代码工作原理
- ✨ **重构建议** - 改善代码结构和可维护性
- 🧪 **编写测试** - 生成单元测试和集成测试

你可以直接输入问题，或者使用下方的快速操作按钮开始对话！`,
      timestamp: new Date()
    }
  ]);
  const [input, setInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isStreaming, setIsStreaming] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const currentStreamingMessage = useRef<string>('');

  const scrollToBottom = () => {
    setTimeout(() => {
      if (messagesEndRef.current) {
        const scrollContainer = messagesEndRef.current.closest('.overflow-y-auto') as HTMLElement;
        if (scrollContainer) {
          scrollContainer.scrollTop = scrollContainer.scrollHeight;
        }
      }
    }, 50);
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  useEffect(() => {
    if (isStreaming) {
      scrollToBottom();
    }
  }, [isStreaming]);

  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      const message = event.data;
      
      switch (message.command) {
        case 'aiStreamChunk':
          setIsStreaming(true);
          currentStreamingMessage.current += message.content;
          
          setMessages(prev => {
            const newMessages = [...prev];
            const lastMessage = newMessages[newMessages.length - 1];
            
            if (lastMessage && lastMessage.role === 'assistant' && lastMessage.id === 'streaming') {
              lastMessage.content = currentStreamingMessage.current;
            } else {
              newMessages.push({
                id: 'streaming',
                role: 'assistant',
                content: currentStreamingMessage.current,
                timestamp: new Date()
              });
            }
            
            return newMessages;
          });
          break;
          
        case 'aiStreamEnd':
          setIsLoading(false);
          setIsStreaming(false);
          
          setMessages(prev => {
            const newMessages = [...prev];
            const lastMessage = newMessages[newMessages.length - 1];
            
            if (lastMessage && lastMessage.id === 'streaming') {
              lastMessage.id = Date.now().toString();
            }
            
            return newMessages;
          });
          
          currentStreamingMessage.current = '';
          break;
          
        case 'aiError':
          setIsLoading(false);
          setIsStreaming(false);
          
          const errorMessage: Message = {
            id: Date.now().toString(),
            role: 'assistant',
            content: `❌ **错误**: ${message.error}`,
            timestamp: new Date()
          };
          
          setMessages(prev => [...prev, errorMessage]);
          currentStreamingMessage.current = '';
          break;
      }
    };

    window.addEventListener('message', handleMessage);
    return () => window.removeEventListener('message', handleMessage);
  }, []);

  const sendMessage = async () => {
    if (!input.trim() || isLoading) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      role: 'user',
      content: input.trim(),
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInput('');
    setIsLoading(true);
    currentStreamingMessage.current = '';

    vscode.postMessage({
      command: 'sendChatMessage',
      message: input.trim(),
      messages: messages.map(msg => ({
        role: msg.role,
        content: msg.content
      }))
    });
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  const clearChat = () => {
    setMessages([]);
    currentStreamingMessage.current = '';
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
    } catch (err) {
      console.error('Failed to copy text: ', err);
    }
  };

  return (
    <div className="h-full max-h-screen grid grid-rows-[auto_1fr_auto] bg-card/50 backdrop-blur-sm border border-border/50 rounded-3xl shadow-2xl overflow-hidden">
      {/* 顶部状态栏 */}
      <div className="flex-shrink-0 flex items-center justify-between p-6 border-b border-border/50 bg-gradient-to-r from-purple-500/5 to-blue-500/5">
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full animate-bounce-subtle shadow-lg"></div>
            <span className="text-sm font-medium text-foreground">AI Assistant Online</span>
          </div>
          <div className="h-4 w-px bg-border/50"></div>
          <span className="text-xs text-muted-foreground">
            {messages.length > 1 ? `${messages.length - 1} 条对话` : '开始新对话'}
          </span>
        </div>
        <Button
          variant="ghost"
          size="sm"
          onClick={clearChat}
          disabled={isLoading}
          className="hover:bg-destructive/10 hover:text-destructive transition-colors"
        >
          <span className="mr-2">🗑️</span>
          清空
        </Button>
      </div>

      {/* 消息区域 */}
      <div className="overflow-y-auto overflow-x-hidden px-6 py-4 min-h-0">
        <div className="space-y-6">
            {messages.map((message, index) => (
              <div
                key={message.id}
                className={cn(
                  "flex gap-4 group animate-fade-in",
                  message.role === 'user' ? 'justify-end' : 'justify-start'
                )}
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                {message.role === 'assistant' && (
                  <div className="relative">
                    <Avatar className="w-12 h-12 shrink-0 ring-2 ring-purple-500/20 shadow-lg">
                      <AvatarFallback className="bg-gradient-to-br from-purple-500 to-blue-600 text-white text-sm font-bold">
                        🤖
                      </AvatarFallback>
                    </Avatar>
                    <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-background animate-bounce-subtle"></div>
                  </div>
                )}

                <div className={cn(
                  "flex flex-col gap-2 max-w-[75%]",
                  message.role === 'user' ? 'items-end' : 'items-start'
                )}>
                  <div className={cn(
                    "relative group/message transition-all duration-300 hover:scale-[1.02] shadow-lg",
                    message.role === 'user'
                      ? 'bg-gradient-to-br from-purple-500 to-blue-600 text-white rounded-3xl rounded-br-lg px-6 py-4 shadow-purple-500/25'
                      : 'bg-card/90 backdrop-blur-sm border border-border/50 text-foreground rounded-3xl rounded-bl-lg px-6 py-4'
                  )}>
                    <MessageContent content={message.content} role={message.role} />

                    <Button
                      variant="ghost"
                      size="sm"
                      className={cn(
                        "absolute -top-2 -right-2 opacity-0 group-hover/message:opacity-100 transition-all duration-200 h-8 w-8 p-0 rounded-full shadow-lg",
                        message.role === 'user'
                          ? 'bg-white/20 hover:bg-white/30 text-white'
                          : 'bg-muted hover:bg-muted/80'
                      )}
                      onClick={() => copyToClipboard(message.content)}
                      title="复制消息"
                    >
                      📋
                    </Button>
                  </div>

                  <div className="flex items-center gap-2 text-xs text-muted-foreground px-2">
                    <span>{message.timestamp.toLocaleTimeString()}</span>
                    {message.role === 'assistant' && (
                      <Badge variant="secondary" className="text-xs bg-purple-500/10 text-purple-400 border-purple-500/20">
                        AI Assistant
                      </Badge>
                    )}
                  </div>
                </div>

                {message.role === 'user' && (
                  <Avatar className="w-12 h-12 shrink-0 ring-2 ring-blue-500/20 shadow-lg">
                    <AvatarFallback className="bg-gradient-to-br from-blue-500 to-cyan-600 text-white text-sm font-bold">
                      👤
                    </AvatarFallback>
                  </Avatar>
                )}
              </div>
            ))}

            {isLoading && !isStreaming && (
              <div className="flex gap-3 justify-start">
                <Avatar className="h-8 w-8 mt-1">
                  <AvatarFallback className="bg-primary text-primary-foreground text-sm">
                    AI
                  </AvatarFallback>
                </Avatar>
                <div className="bg-muted rounded-lg px-4 py-3">
                  <div className="flex items-center gap-1">
                    <div className="flex space-x-1">
                      <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce [animation-delay:-0.3s]"></div>
                      <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce [animation-delay:-0.15s]"></div>
                      <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce"></div>
                    </div>
                    <span className="text-sm text-muted-foreground ml-2">AI 正在思考...</span>
                  </div>
                </div>
              </div>
            )}

          <div ref={messagesEndRef} className="h-4" />
        </div>
      </div>

      {/* 输入区域 */}
      <div className="border-t border-border/50 p-6 bg-gradient-to-r from-background/80 to-background/60 backdrop-blur-sm">
        <div className="space-y-4">
          <div className="flex gap-4 items-end">
            <div className="flex-1 relative">
              <Textarea
                value={input}
                onChange={(e) => setInput(e.target.value)}
                onKeyDown={handleKeyDown}
                placeholder="✨ 输入你的问题，让 AI 助手帮助你..."
                disabled={isLoading}
                className="min-h-[80px] max-h-[200px] resize-none bg-card/50 backdrop-blur-sm border-border/50 rounded-2xl text-base placeholder:text-muted-foreground/60 focus:ring-2 focus:ring-purple-500/20 focus:border-purple-500/50 transition-all duration-200 pr-16"
                rows={3}
              />

              {input.length > 0 && (
                <div className="absolute bottom-3 right-3">
                  <span className={cn(
                    "text-xs font-medium px-2 py-1 rounded-full",
                    input.length > 1000
                      ? 'text-red-400 bg-red-500/10'
                      : 'text-muted-foreground bg-muted/50'
                  )}>
                    {input.length}/2000
                  </span>
                </div>
              )}
            </div>

            <Button
              onClick={sendMessage}
              disabled={!input.trim() || isLoading}
              className="btn-gradient h-[80px] px-8 py-3 rounded-2xl font-medium text-base shadow-lg hover:shadow-xl transition-all duration-300"
            >
              {isLoading ? (
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                  思考中...
                </div>
              ) : (
                <div className="flex items-center gap-2">
                  <span>发送</span>
                  <span className="text-lg">🚀</span>
                </div>
              )}
            </Button>
          </div>
          <div className="flex items-center justify-center gap-6 text-xs text-muted-foreground/60">
            <div className="flex items-center gap-2">
              <span>💡</span>
              <kbd className="px-2 py-1 bg-muted/50 rounded-lg text-xs font-mono">Enter</kbd>
              <span>发送消息</span>
            </div>
            <div className="w-px h-4 bg-border/50"></div>
            <div className="flex items-center gap-2">
              <kbd className="px-2 py-1 bg-muted/50 rounded-lg text-xs font-mono">Shift</kbd>
              <span>+</span>
              <kbd className="px-2 py-1 bg-muted/50 rounded-lg text-xs font-mono">Enter</kbd>
              <span>换行</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChatInterface;
