import React from 'react';
import ReactMarkdown from 'react-markdown';
import { cn } from '../lib/utils';

interface MessageContentProps {
  content: string;
  role: 'user' | 'assistant';
}

const MessageContent: React.FC<MessageContentProps> = ({ content, role }) => {
  if (role === 'user') {
    return (
      <div className="whitespace-pre-wrap break-words">
        {content}
      </div>
    );
  }

  return (
    <div className="prose prose-sm max-w-none dark:prose-invert">
      <ReactMarkdown
        components={{
          // 自定义代码块样式
          code: ({ node, className, children, ...props }: any) => {
            const match = /language-(\w+)/.exec(className || '');
            const language = match ? match[1] : '';
            const inline = !className || !className.startsWith('language-');

            return !inline ? (
              <div className="relative group my-4">
                {language && (
                  <div className="flex items-center justify-between bg-muted/50 px-3 py-1 rounded-t-md border border-b-0 border-border">
                    <span className="text-xs font-medium text-muted-foreground uppercase">
                      {language}
                    </span>
                    <button
                      onClick={() => {
                        navigator.clipboard.writeText(String(children));
                      }}
                      className="opacity-0 group-hover:opacity-100 transition-opacity text-xs bg-background hover:bg-accent px-2 py-1 rounded text-muted-foreground hover:text-foreground"
                      title="复制代码"
                    >
                      📋 复制
                    </button>
                  </div>
                )}
                <pre className={cn(
                  "bg-muted rounded-md p-3 overflow-x-auto text-sm font-mono",
                  "border border-border",
                  language ? "rounded-t-none" : ""
                )}>
                  <code className={className} {...props}>
                    {children}
                  </code>
                </pre>
              </div>
            ) : (
              <code
                className={cn(
                  "bg-muted px-1.5 py-0.5 rounded text-sm font-mono",
                  "border border-border"
                )}
                {...props}
              >
                {children}
              </code>
            );
          },
          // 自定义段落样式
          p: ({ children }) => (
            <p className="mb-3 last:mb-0 leading-relaxed">
              {children}
            </p>
          ),
          // 自定义列表样式
          ul: ({ children }) => (
            <ul className="mb-3 pl-4 space-y-1">
              {children}
            </ul>
          ),
          ol: ({ children }) => (
            <ol className="mb-3 pl-4 space-y-1">
              {children}
            </ol>
          ),
          li: ({ children }) => (
            <li className="leading-relaxed">
              {children}
            </li>
          ),
          // 自定义标题样式
          h1: ({ children }) => (
            <h1 className="text-lg font-semibold mb-2 mt-4 first:mt-0">
              {children}
            </h1>
          ),
          h2: ({ children }) => (
            <h2 className="text-base font-semibold mb-2 mt-3 first:mt-0">
              {children}
            </h2>
          ),
          h3: ({ children }) => (
            <h3 className="text-sm font-semibold mb-2 mt-3 first:mt-0">
              {children}
            </h3>
          ),
          // 自定义引用样式
          blockquote: ({ children }) => (
            <blockquote className="border-l-4 border-border pl-4 italic text-muted-foreground mb-3">
              {children}
            </blockquote>
          ),
          // 自定义表格样式
          table: ({ children }) => (
            <div className="overflow-x-auto mb-3">
              <table className="min-w-full border border-border rounded-md">
                {children}
              </table>
            </div>
          ),
          th: ({ children }) => (
            <th className="border border-border px-3 py-2 bg-muted font-semibold text-left">
              {children}
            </th>
          ),
          td: ({ children }) => (
            <td className="border border-border px-3 py-2">
              {children}
            </td>
          ),
        }}
      >
        {content}
      </ReactMarkdown>
    </div>
  );
};

export default MessageContent;
