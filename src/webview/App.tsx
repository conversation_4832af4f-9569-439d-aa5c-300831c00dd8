import React, { useState } from 'react';
import './App.css';
import ChatInterface from './components/ChatInterface';

// 声明vscode API类型
declare const acquireVsCodeApi: () => {
  postMessage(message: any): void;
  getState(): any;
  setState(state: any): void;
};



const App: React.FC = () => {
  const [vscode] = useState(() => acquireVsCodeApi());



  return (
    <div className="h-screen bg-background relative overflow-hidden">
      {/* 背景装饰 */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 rounded-full bg-gradient-to-br from-purple-500/10 to-blue-500/10 blur-3xl"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 rounded-full bg-gradient-to-tr from-blue-500/10 to-cyan-500/10 blur-3xl"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 rounded-full bg-gradient-to-r from-purple-500/5 to-pink-500/5 blur-3xl"></div>
      </div>

      <div className="relative z-10 container mx-auto max-w-5xl p-6 h-full flex flex-col">
        {/* 顶部标题区域 */}
        <div className="mb-8 text-center animate-fade-in">
          <div className="inline-flex items-center gap-3 mb-4">
            <div className="w-12 h-12 rounded-2xl bg-gradient-to-br from-purple-500 to-blue-600 flex items-center justify-center shadow-lg">
              <span className="text-2xl">🤖</span>
            </div>
            <div className="text-left">
              <h1 className="text-3xl font-bold bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent">
                AI Code Assistant
              </h1>
              <p className="text-muted-foreground text-sm">智能代码助手，让编程更高效</p>
            </div>
          </div>
        </div>

        {/* 聊天界面 */}
        <div className="flex-1 min-h-0 animate-slide-up">
          <ChatInterface vscode={vscode} />
        </div>
      </div>
    </div>
  );
};

export default App;
