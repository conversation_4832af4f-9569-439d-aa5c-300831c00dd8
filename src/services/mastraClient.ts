export interface MastraMessage {
    role: 'user' | 'assistant' | 'system';
    content: string;
}

export interface MastraGenerateRequest {
    messages: MastraMessage[];
    stream?: boolean;
}

export interface MastraGenerateResponse {
    content: string;
    usage?: {
        promptTokens: number;
        completionTokens: number;
        totalTokens: number;
    };
}

export interface MastraAgent {
    id: string;
    name: string;
    instructions: string;
    model: string;
}

export interface MastraTool {
    id: string;
    name: string;
    description: string;
    parameters: any;
}

export class MastraClient {
    private baseUrl: string;

    constructor(baseUrl: string = 'http://localhost:4111') {
        this.baseUrl = baseUrl;
    }

    // Agent 相关方法
    async getAgents(): Promise<MastraAgent[]> {
        const response = await fetch(`${this.baseUrl}/api/agents`);
        if (!response.ok) {
            throw new Error(`获取 Agents 失败: ${response.statusText}`);
        }
        return response.json();
    }

    async getAgent(agentId: string): Promise<MastraAgent> {
        const response = await fetch(`${this.baseUrl}/api/agents/${agentId}`);
        if (!response.ok) {
            throw new Error(`获取 Agent ${agentId} 失败: ${response.statusText}`);
        }
        return response.json();
    }

    async generateWithAgent(agentId: string, request: MastraGenerateRequest): Promise<MastraGenerateResponse> {
        const response = await fetch(`${this.baseUrl}/api/agents/${agentId}/generate`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(request),
        });

        if (!response.ok) {
            throw new Error(`Agent 生成失败: ${response.statusText}`);
        }

        return response.json();
    }

    async streamWithAgent(agentId: string, request: MastraGenerateRequest): Promise<ReadableStream> {
        const response = await fetch(`${this.baseUrl}/api/agents/${agentId}/stream`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(request),
        });

        if (!response.ok) {
            throw new Error(`Agent 流式生成失败: ${response.statusText}`);
        }

        if (!response.body) {
            throw new Error('响应体为空');
        }

        return response.body;
    }

    // Tool 相关方法
    async getTools(): Promise<MastraTool[]> {
        const response = await fetch(`${this.baseUrl}/api/tools`);
        if (!response.ok) {
            throw new Error(`获取 Tools 失败: ${response.statusText}`);
        }
        return response.json();
    }

    async getTool(toolId: string): Promise<MastraTool> {
        const response = await fetch(`${this.baseUrl}/api/tools/${toolId}`);
        if (!response.ok) {
            throw new Error(`获取 Tool ${toolId} 失败: ${response.statusText}`);
        }
        return response.json();
    }

    async executeTool(toolId: string, input: any): Promise<any> {
        const response = await fetch(`${this.baseUrl}/api/tools/${toolId}/execute`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(input),
        });

        if (!response.ok) {
            throw new Error(`执行 Tool ${toolId} 失败: ${response.statusText}`);
        }

        return response.json();
    }

    // 健康检查
    async healthCheck(): Promise<boolean> {
        try {
            const response = await fetch(`${this.baseUrl}/api`, {
                method: 'GET',
                timeout: 5000,
            } as any);
            return response.ok;
        } catch (error) {
            return false;
        }
    }

    // 获取服务器状态
    async getServerStatus(): Promise<any> {
        const response = await fetch(`${this.baseUrl}/api`);
        if (!response.ok) {
            throw new Error(`获取服务器状态失败: ${response.statusText}`);
        }
        return response.json();
    }
}

// 创建默认客户端实例
export const mastraClient = new MastraClient();
