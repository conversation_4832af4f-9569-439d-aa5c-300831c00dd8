
import { generateText, streamText, CoreMessage } from 'ai';
import * as vscode from 'vscode';
import { AgentService } from '../types/agent';
import { openai } from '../llm/open';
import { mastra } from '../mastra';
import { mastraClient, MastraMessage } from './mastraClient';





export class MastraAIService implements AgentService {
    private apiKey: string | undefined;

    constructor() {
        this.apiKey = vscode.workspace.getConfiguration('aicode').get('openaiApiKey');
    }

    private getOpenAIModel() {
        if (!this.apiKey) {
            throw new Error('OpenAI API key not configured');
        }
        return openai('deepseek-ai/DeepSeek-V3');
    }

    async query(
        prompt?: string,
        messages?: CoreMessage[],
        options?: any,
        abortController?: AbortController,
        onMessage?: (message: any) => void
    ): Promise<any[]> {
        if (!this.hasApiKey()) {
            throw new Error('OpenAI API key not configured. Please set it in VS Code settings.');
        }

        try {
            const model = this.getOpenAIModel();
         
            // 如果有流式回调，使用 streamText
            if (onMessage) {

              const weatherAgent =  mastra.getAgent('weatherAgent');
              const result = await  weatherAgent.stream(messages || [{ role: 'user', content: prompt || '' }]);
              // const result =  streamText({
                //     model: model,
                //     messages: messages || [{ role: 'user', content: prompt || '' }],
                //     abortSignal: abortController?.signal,
                // });


                const chunks: any[] = [];
                for await (const chunk of result.textStream) {
                    chunks.push({ type: 'text', content: chunk });
                    onMessage({ type: 'text', content: chunk });
                }
                return chunks;
            } else {
                // 否则使用 generateText
                const result = await generateText({
                    model: model,
                    messages: messages || [{ role: 'user', content: prompt || '' }],
                    abortSignal: abortController?.signal,
                });
                return [{ type: 'text', content: result.text }];
            }
        } catch (error: any) {
            if (this.isApiKeyAuthError(error.message)) {
                throw new Error('Invalid OpenAI API key. Please check your configuration.');
            }
            throw error;
        }
    }

    hasApiKey(): boolean {
        return !!this.apiKey && this.apiKey.trim().length > 0;
    }

    isApiKeyAuthError(errorMessage: string): boolean {
        const message = errorMessage.toLowerCase();
        return message.includes('401') ||
            message.includes('unauthorized') ||
            message.includes('invalid api key') ||
            message.includes('authentication');
    }

    // 更新 API Key
    updateApiKey(apiKey: string) {
        this.apiKey = apiKey;
    }

    // 获取当前配置的模型信息
    getModelInfo() {
        return {
            name: 'DeepSeek-V3',
            provider: 'DeepSeek AI',
            description: 'Advanced language model for coding assistance'
        };
    }
}

// 新的基于 Mastra 客户端的 AI 服务
export class MastraClientAIService implements AgentService {
    private defaultAgentId: string = 'weatherAgent';

    constructor(private agentId?: string) {
        this.defaultAgentId = agentId || 'weatherAgent';
    }

    async query(
        prompt?: string,
        messages?: CoreMessage[],
        options?: any,
        abortController?: AbortController,
        onMessage?: (message: any) => void
    ): Promise<any[]> {
        try {
            // 检查 Mastra 服务器是否可用
            const isHealthy = await mastraClient.healthCheck();
            if (!isHealthy) {
                throw new Error('Mastra 服务器不可用，请确保服务器已启动');
            }

            // 转换消息格式
            const mastraMessages: MastraMessage[] = messages?.map(msg => ({
                role: msg.role as 'user' | 'assistant' | 'system',
                content: msg.content as string
            })) || [{ role: 'user', content: prompt || '' }];

            // 如果有流式回调，使用流式 API
            if (onMessage) {
                return await this.handleStreamResponse(mastraMessages, onMessage);
            } else {
                // 否则使用普通生成 API
                const result = await mastraClient.generateWithAgent(this.defaultAgentId, {
                    messages: mastraMessages
                });

                return [{ type: 'text', content: result.content }];
            }
        } catch (error: any) {
            throw error;
        }
    }

    private async handleStreamResponse(messages: MastraMessage[], onMessage: (message: any) => void): Promise<any[]> {
        const stream = await mastraClient.streamWithAgent(this.defaultAgentId, {
            messages: messages,
            stream: true
        });

        const chunks: any[] = [];
        const reader = stream.getReader();
        const decoder = new TextDecoder();

        try {
            while (true) {
                const { done, value } = await reader.read();
                if (done) {
                    break;
                }

                const chunk = decoder.decode(value, { stream: true });
                const lines = chunk.split('\n');

                for (const line of lines) {
                    if (line.trim() && line.startsWith('data: ')) {
                        try {
                            const data = JSON.parse(line.slice(6));
                            if (data.content) {
                                chunks.push({ type: 'text', content: data.content });
                                onMessage({ type: 'text', content: data.content });
                            }
                        } catch (e) {
                            // 忽略解析错误
                        }
                    }
                }
            }
        } finally {
            reader.releaseLock();
        }

        return chunks;
    }

    hasApiKey(): boolean {
        // Mastra 服务器管理 API 密钥，这里总是返回 true
        return true;
    }

    isApiKeyAuthError(errorMessage: string): boolean {
        const message = errorMessage.toLowerCase();
        return message.includes('api key') ||
               message.includes('authentication') ||
               message.includes('unauthorized');
    }

    updateApiKey(apiKey: string) {
        // Mastra 服务器管理 API 密钥，这里不需要实现
    }

    getModelInfo() {
        return {
            name: 'Mastra Agent',
            provider: 'Mastra Framework',
            description: 'AI agent powered by Mastra framework'
        };
    }

    // 获取可用的 agents
    async getAvailableAgents() {
        try {
            return await mastraClient.getAgents();
        } catch (error) {
            return [];
        }
    }

    // 切换 agent
    setAgent(agentId: string) {
        this.defaultAgentId = agentId;
    }
}
