// 简单的测试webview provider
import * as vscode from 'vscode';

export class SimpleWebviewProvider implements vscode.WebviewViewProvider {
    constructor(private readonly _extensionUri: vscode.Uri) {}

    public resolveWebviewView(
        webviewView: vscode.WebviewView,
        context: vscode.WebviewViewResolveContext,
        _token: vscode.CancellationToken,
    ) {
        webviewView.webview.options = {
            enableScripts: true,
            localResourceRoots: [this._extensionUri]
        };

        webviewView.webview.html = this.getHtmlForWebview();
        
        webviewView.webview.onDidReceiveMessage(
            message => {
                switch (message.command) {
                    case 'alert':
                        vscode.window.showInformationMessage(message.text);
                        return;
                }
            },
            undefined,
            []
        );
    }

    private getHtmlForWebview(): string {
        return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Test</title>
    <style>
        body {
            font-family: var(--vscode-font-family);
            color: var(--vscode-foreground);
            background-color: var(--vscode-editor-background);
            padding: 20px;
        }
        button {
            background-color: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
            border: none;
            padding: 8px 16px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: var(--vscode-button-hoverBackground);
        }
    </style>
</head>
<body>
    <h1>简单测试页面</h1>
    <p>如果您能看到这个页面，说明webview provider工作正常！</p>
    <button onclick="sendMessage()">发送测试消息</button>
    <div id="counter">计数: <span id="count">0</span></div>
    <button onclick="increment()">增加</button>
    
    <script>
        const vscode = acquireVsCodeApi();
        let count = 0;
        
        function sendMessage() {
            vscode.postMessage({
                command: 'alert',
                text: '来自简单webview的消息！'
            });
        }
        
        function increment() {
            count++;
            document.getElementById('count').textContent = count;
        }
        

    </script>
</body>
</html>`;
    }
}
